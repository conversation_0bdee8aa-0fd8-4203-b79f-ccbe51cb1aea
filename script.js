// Global variables
let allPalettes = [];
let filteredPalettes = [];
let currentCategory = 'all';
let currentView = 'grid';
let currentPage = 1;
const palettesPerPage = 24;
let searchQuery = '';

// DOM elements
const paletteGrid = document.getElementById('paletteGrid');
const categoryFilters = document.getElementById('categoryFilters');
const searchInput = document.getElementById('searchInput');
const clearSearch = document.getElementById('clearSearch');
const loading = document.getElementById('loading');
const errorState = document.getElementById('errorState');
const emptyState = document.getElementById('emptyState');
const sectionTitle = document.getElementById('sectionTitle');
const sectionSubtitle = document.getElementById('sectionSubtitle');
const loadMoreContainer = document.getElementById('loadMoreContainer');
const loadMoreBtn = document.getElementById('loadMoreBtn');
const sidebar = document.getElementById('sidebar');
const toggleFilters = document.getElementById('toggleFilters');
const closeSidebar = document.getElementById('closeSidebar');
const resetFilters = document.getElementById('resetFilters');
const gridView = document.getElementById('gridView');
const listView = document.getElementById('listView');
const toast = document.getElementById('toast');
const modalOverlay = document.getElementById('modalOverlay');
const colorModal = document.getElementById('colorModal');
const closeModal = document.getElementById('closeModal');
const colorPreview = document.getElementById('colorPreview');
const hexCode = document.getElementById('hexCode');
const copyHex = document.getElementById('copyHex');

// Initialize the application
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await loadColorData();
        setupEventListeners();
        renderCategoryFilters();
        renderPalettes();
    } catch (error) {
        console.error('Failed to initialize app:', error);
        showErrorState();
    }
});

// Load color data from JSON file
async function loadColorData() {
    try {
        const response = await fetch('colors.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        allPalettes = await response.json();
        filteredPalettes = [...allPalettes];
        
        // Generate additional palettes to reach ~10,000
        generateAdditionalPalettes();
        
        hideLoading();
    } catch (error) {
        console.error('Error loading color data:', error);
        throw error;
    }
}

// Generate additional color palettes programmatically
function generateAdditionalPalettes() {
    const categories = [...new Set(allPalettes.map(p => p.category))];
    const targetCount = 20000;
    const currentCount = allPalettes.length;
    
    if (currentCount >= targetCount) return;
    
    const additionalPalettes = [];
    const palettesToGenerate = targetCount - currentCount;
    
    for (let i = 0; i < palettesToGenerate; i++) {
        const category = categories[i % categories.length];
        const palette = generateRandomPalette(category);
        additionalPalettes.push(palette);
    }
    
    allPalettes.push(...additionalPalettes);
    filteredPalettes = [...allPalettes];
}

// Generate a random color palette for a given category
function generateRandomPalette(category) {
    const baseColors = getCategoryBaseColors(category);
    const colors = [];
    
    for (let i = 0; i < 4; i++) {
        const baseColor = baseColors[i % baseColors.length];
        const variation = generateColorVariation(baseColor);
        colors.push(variation);
    }
    
    return { category, colors };
}

// Get base colors for each category
function getCategoryBaseColors(category) {
    const categoryColorMap = {
        'Vintage': ['#F5E6D3', '#E8B4A0', '#D2691E', '#8B4513'],
        'Retro': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
        'Neon': ['#FF0080', '#00FF80', '#8000FF', '#FF8000'],
        'Gold': ['#FFD700', '#FFA500', '#FF8C00', '#DAA520'],
        'Light': ['#F8F9FA', '#E9ECEF', '#DEE2E6', '#CED4DA'],
        'Dark': ['#212529', '#343A40', '#495057', '#6C757D'],
        'Warm': ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5'],
        'Cold': ['#0077BE', '#00A8CC', '#7FDBFF', '#B8E6B8'],
        'Summer': ['#FFE082', '#FFCC02', '#FF6F00', '#E65100'],
        'Fall': ['#D84315', '#FF5722', '#FF8A65', '#FFAB91'],
        'Winter': ['#E3F2FD', '#BBDEFB', '#90CAF9', '#64B5F6'],
        'Spring': ['#C8E6C9', '#A5D6A7', '#81C784', '#66BB6A'],
        'Happy': ['#FFEB3B', '#FFC107', '#FF9800', '#FF5722'],
        'Nature': ['#2E7D32', '#388E3C', '#43A047', '#4CAF50'],
        'Earth': ['#3E2723', '#4E342E', '#5D4037', '#6D4C41'],
        'Night': ['#0D47A1', '#1565C0', '#1976D2', '#1E88E5'],
        'Space': ['#000000', '#1A1A2E', '#16213E', '#0F3460'],
        'Rainbow': ['#FF0000', '#FF8000', '#FFFF00', '#80FF00'],
        'Gradient': ['#667eea', '#764ba2', '#f093fb', '#f5576c'],
        'Sunset': ['#FF6B6B', '#FF8E53', '#FF6B9D', '#C44569'],
        'Sky': ['#E3F2FD', '#BBDEFB', '#90CAF9', '#64B5F6'],
        'Sea': ['#006064', '#00838F', '#0097A7', '#00ACC1'],
        'Kids': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
        'Skin': ['#FDBCB4', '#F1C27D', '#E0AC69', '#C68642'],
        'Food': ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5'],
        'Cream': ['#FFF8E1', '#FFECB3', '#FFE082', '#FFD54F'],
        'Coffee': ['#3E2723', '#4E342E', '#5D4037', '#6D4C41'],
        'Wedding': ['#F8BBD9', '#F48FB1', '#F06292', '#EC407A'],
        'Christmas': ['#C62828', '#2E7D32', '#FFD700', '#FFFFFF'],
        'Halloween': ['#FF6F00', '#000000', '#6A1B9A', '#4E342E'],
        'Pastel': ['#FFB3BA', '#FFDFBA', '#FFFFBA', '#BAFFC9'],
        'Monochrome': ['#000000', '#404040', '#808080', '#C0C0C0'],
        'Tropical': ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
        'Corporate': ['#1E3A8A', '#3B82F6', '#60A5FA', '#DBEAFE'],
        'Minimalist': ['#FFFFFF', '#F8F9FA', '#E9ECEF', '#6C757D'],
        'Vibrant': ['#FF0040', '#FF8000', '#FFFF00', '#80FF00'],
        'Muted': ['#8E8E93', '#AEAEB2', '#C7C7CC', '#D1D1D6'],
        'Ocean': ['#001F3F', '#0074D9', '#7FDBFF', '#39CCCC'],
        'Desert': ['#8B4513', '#CD853F', '#DEB887', '#F5DEB3'],
        'Forest': ['#0B5345', '#148F77', '#52C4A0', '#A3E4D7'],
        'Aurora': ['#00FF7F', '#00CED1', '#9370DB', '#FF1493'],
        'Cyberpunk': ['#FF0080', '#00FFFF', '#FFFF00', '#8000FF'],
        'Luxury': ['#FFD700', '#C0C0C0', '#000000', '#FFFFFF'],
        'Candy': ['#FF69B4', '#FFB6C1', '#FFC0CB', '#FFCCCB'],
        'Metallic': ['#C0C0C0', '#A9A9A9', '#808080', '#696969'],
        'Jewel': ['#8B0000', '#006400', '#000080', '#800080'],
        'Smoke': ['#2F4F4F', '#696969', '#A9A9A9', '#D3D3D3'],
        'Fire': ['#8B0000', '#DC143C', '#FF4500', '#FFA500'],
        'Ice': ['#E0FFFF', '#AFEEEE', '#87CEEB', '#4682B4'],
        'Electric': ['#00FFFF', '#00BFFF', '#0080FF', '#0040FF'],
        'Cosmic': ['#191970', '#4B0082', '#8A2BE2', '#9400D3'],
        'Floral': ['#FFB6C1', '#FFC0CB', '#FFCCCB', '#FFE4E1'],
        'Rustic': ['#8B4513', '#A0522D', '#CD853F', '#DEB887'],
        'Urban': ['#2F2F2F', '#5A5A5A', '#8B8B8B', '#BEBEBE'],
        'Zen': ['#F5F5DC', '#E6E6FA', '#F0F8FF', '#F8F8FF'],
        'Gaming': ['#00FF00', '#FF0000', '#0000FF', '#FFFF00'],
        'Artistic': ['#FF6347', '#4682B4', '#32CD32', '#FFD700'],
        'Medical': ['#FFFFFF', '#E0E0E0', '#87CEEB', '#32CD32'],
        'Sports': ['#FF0000', '#0000FF', '#FFFF00', '#00FF00'],
        'Academic': ['#000080', '#8B0000', '#FFD700', '#FFFFFF'],
        'Travel': ['#87CEEB', '#F0E68C', '#DDA0DD', '#98FB98'],
        'Music': ['#8A2BE2', '#FF1493', '#00CED1', '#FFD700'],
        'Technology': ['#000000', '#0080FF', '#00FF80', '#FFFFFF']
    };
    
    return categoryColorMap[category] || ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
}

// Generate a color variation
function generateColorVariation(baseColor) {
    const hex = baseColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Add random variation (-40 to +40) for more diversity
    const variation = 40;
    const newR = Math.max(0, Math.min(255, r + (Math.random() * variation * 2 - variation)));
    const newG = Math.max(0, Math.min(255, g + (Math.random() * variation * 2 - variation)));
    const newB = Math.max(0, Math.min(255, b + (Math.random() * variation * 2 - variation)));
    
    return `#${Math.round(newR).toString(16).padStart(2, '0')}${Math.round(newG).toString(16).padStart(2, '0')}${Math.round(newB).toString(16).padStart(2, '0')}`;
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    searchInput.addEventListener('input', handleSearch);
    clearSearch.addEventListener('click', clearSearchInput);
    
    // Filter functionality
    resetFilters.addEventListener('click', resetAllFilters);
    
    // View toggle
    gridView.addEventListener('click', () => setView('grid'));
    listView.addEventListener('click', () => setView('list'));
    
    // Mobile sidebar
    toggleFilters.addEventListener('click', toggleSidebar);
    closeSidebar.addEventListener('click', closeSidebarMobile);
    
    // Load more
    loadMoreBtn.addEventListener('click', loadMorePalettes);
    
    // Modal
    closeModal.addEventListener('click', closeColorModal);
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) closeColorModal();
    });
    copyHex.addEventListener('click', () => copyToClipboard(hexCode.value));
    
    // Keyboard navigation
    document.addEventListener('keydown', handleKeyboard);
}

// Handle search input
function handleSearch(e) {
    searchQuery = e.target.value.toLowerCase().trim();
    clearSearch.style.display = searchQuery ? 'block' : 'none';
    currentPage = 1;
    filterPalettes();
    renderPalettes();
}

// Clear search input
function clearSearchInput() {
    searchInput.value = '';
    searchQuery = '';
    clearSearch.style.display = 'none';
    currentPage = 1;
    filterPalettes();
    renderPalettes();
}

// Filter palettes based on category and search
function filterPalettes() {
    filteredPalettes = allPalettes.filter(palette => {
        const matchesCategory = currentCategory === 'all' || palette.category === currentCategory;
        const matchesSearch = !searchQuery || 
            palette.category.toLowerCase().includes(searchQuery) ||
            palette.colors.some(color => color.toLowerCase().includes(searchQuery));
        
        return matchesCategory && matchesSearch;
    });
    
    updateSectionInfo();
}

// Update section title and subtitle
function updateSectionInfo() {
    const count = filteredPalettes.length;
    
    if (searchQuery) {
        sectionTitle.textContent = `Search Results for "${searchInput.value}"`;
        sectionSubtitle.textContent = `Found ${count.toLocaleString()} palette${count !== 1 ? 's' : ''}`;
    } else if (currentCategory === 'all') {
        sectionTitle.textContent = 'All Color Palettes';
        sectionSubtitle.textContent = `Discover ${count.toLocaleString()} beautiful color combinations for your next project`;
    } else {
        sectionTitle.textContent = `${currentCategory} Palettes`;
        sectionSubtitle.textContent = `${count.toLocaleString()} curated ${currentCategory.toLowerCase()} color combinations`;
    }
}

// Render category filters
function renderCategoryFilters() {
    const categories = [...new Set(allPalettes.map(p => p.category))].sort();
    
    categoryFilters.innerHTML = categories.map(category => {
        const count = allPalettes.filter(p => p.category === category).length;
        return `
            <button class="filter-btn" data-category="${category}">
                <span class="filter-name">${category}</span>
                <span class="filter-count">${count.toLocaleString()}</span>
            </button>
        `;
    }).join('');
    
    // Update all count
    document.getElementById('allCount').textContent = allPalettes.length.toLocaleString();
    
    // Add event listeners
    categoryFilters.addEventListener('click', handleCategoryFilter);

    // Add event listener for "All Palettes" button
    document.querySelector('.filter-btn[data-category="all"]').addEventListener('click', () => {
        setCategory('all');
    });
}

// Handle category filter click
function handleCategoryFilter(e) {
    const filterBtn = e.target.closest('.filter-btn');
    if (!filterBtn) return;

    const category = filterBtn.dataset.category;
    setCategory(category);
}



// Set active category
function setCategory(category) {
    currentCategory = category;
    currentPage = 1;
    
    // Update active filter button
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    if (category === 'all') {
        document.querySelector('.filter-btn[data-category="all"]').classList.add('active');
    } else {
        document.querySelector(`.filter-btn[data-category="${category}"]`).classList.add('active');
    }
    
    filterPalettes();
    renderPalettes();
    
    // Close mobile sidebar
    if (window.innerWidth <= 768) {
        closeSidebarMobile();
    }
}

// Reset all filters
function resetAllFilters() {
    currentCategory = 'all';
    searchQuery = '';
    searchInput.value = '';
    clearSearch.style.display = 'none';
    currentPage = 1;
    
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector('.filter-btn[data-category="all"]').classList.add('active');
    
    filterPalettes();
    renderPalettes();
}

// Set view mode
function setView(view) {
    currentView = view;
    
    gridView.classList.toggle('active', view === 'grid');
    listView.classList.toggle('active', view === 'list');
    paletteGrid.classList.toggle('list-view', view === 'list');
}

// Toggle mobile sidebar
function toggleSidebar() {
    sidebar.classList.add('show');
}

// Close mobile sidebar
function closeSidebarMobile() {
    sidebar.classList.remove('show');
}

// Render palettes
function renderPalettes() {
    const startIndex = 0;
    const endIndex = currentPage * palettesPerPage;
    const palettesToShow = filteredPalettes.slice(startIndex, endIndex);
    
    if (palettesToShow.length === 0) {
        showEmptyState();
        return;
    }
    
    hideEmptyState();
    
    paletteGrid.innerHTML = palettesToShow.map((palette, index) => 
        createPaletteCard(palette, index)
    ).join('');
    
    // Show/hide load more button
    const hasMore = endIndex < filteredPalettes.length;
    loadMoreContainer.style.display = hasMore ? 'block' : 'none';
    
    // Add event listeners to palette cards
    addPaletteEventListeners();
}

// Create palette card HTML
function createPaletteCard(palette, index) {
    return `
        <div class="palette-card" data-index="${index}" tabindex="0">
            <div class="color-blocks">
                ${palette.colors.map((color) => `
                    <div class="color-block" style="background-color: ${color}" data-color="${color}">
                        <div class="color-overlay">
                            <div class="color-info">
                                <div class="hex-code">${color}</div>
                                <button class="copy-color-btn" data-color="${color}">Copy</button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
            <div class="palette-info">
                <div class="palette-category">${palette.category}</div>
            </div>
        </div>
    `;
}

// Add event listeners to palette cards
function addPaletteEventListeners() {
    // Copy color buttons
    document.querySelectorAll('.copy-color-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            const color = btn.dataset.color;
            copyToClipboard(color);
        });
    });
    
    // Color block clicks for modal
    document.querySelectorAll('.color-block').forEach(block => {
        block.addEventListener('click', (e) => {
            e.stopPropagation();
            const color = block.dataset.color;
            showColorModal(color);
        });
    });
}

// Load more palettes
function loadMorePalettes() {
    currentPage++;
    renderPalettes();
}

// Show color modal
function showColorModal(color) {
    colorPreview.style.backgroundColor = color;
    hexCode.value = color;
    modalOverlay.classList.add('show');
}

// Close color modal
function closeColorModal() {
    modalOverlay.classList.remove('show');
}

// Copy to clipboard
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast(`${text} copied to clipboard!`);
    } catch (err) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast(`${text} copied to clipboard!`);
    }
}

// Show toast notification
function showToast(message) {
    document.getElementById('toastMessage').textContent = message;
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// Handle keyboard navigation
function handleKeyboard(e) {
    if (e.key === 'Escape') {
        if (modalOverlay.classList.contains('show')) {
            closeColorModal();
        } else if (sidebar.classList.contains('show')) {
            closeSidebarMobile();
        }
    }
}

// Show/hide loading state
function hideLoading() {
    loading.style.display = 'none';
}

function showErrorState() {
    loading.style.display = 'none';
    errorState.style.display = 'flex';
}

function showEmptyState() {
    emptyState.style.display = 'flex';
    loadMoreContainer.style.display = 'none';
}

function hideEmptyState() {
    emptyState.style.display = 'none';
}
