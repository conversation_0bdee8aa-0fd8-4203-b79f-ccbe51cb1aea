# ColorPalette Pro

A responsive, professional color palette website inspired by ColorHunt.co, built with HTML, CSS, and JavaScript.

## Features

🎨 **10,000+ Color Palettes** - Extensive collection of curated color combinations
📱 **Fully Responsive** - Works perfectly on desktop, tablet, and mobile devices
🔍 **Advanced Search** - Search by color code or category name
🏷️ **25+ Categories** - Organized into themes like Vintage, Retro, Neon, Nature, etc.
📋 **One-Click Copy** - Copy any color code to clipboard instantly
🎯 **Smart Filtering** - Filter palettes by category with live counts
👀 **Multiple Views** - Switch between grid and list view modes
🌙 **Modern Design** - Clean, professional interface with smooth animations
♿ **Accessible** - Keyboard navigation and screen reader friendly
📊 **Performance Optimized** - Lazy loading and efficient rendering

## Categories

- **Vintage & Retro** - Classic and nostalgic color schemes
- **Modern & Neon** - Bright, contemporary palettes
- **Nature & Earth** - Organic, natural color combinations
- **Seasonal** - Summer, Fall, Winter, Spring themes
- **Mood-based** - Happy, Dark, Light, Warm, Cold
- **Special Occasions** - Wedding, Christmas, Halloween
- **Professional** - Corporate, Minimalist, Monochrome
- **Creative** - Rainbow, Gradient, Vibrant, Pastel
- **Lifestyle** - Food, Coffee, Cream, Kids, Skin tones

## Technology Stack

- **HTML5** - Semantic markup with accessibility features
- **CSS3** - Modern styling with CSS Grid, Flexbox, and custom properties
- **Vanilla JavaScript** - No frameworks, pure ES6+ JavaScript
- **JSON Data** - External data file for easy expansion
- **Progressive Enhancement** - Works without JavaScript for basic functionality

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # Complete CSS styling
├── script.js           # JavaScript functionality
├── colors.json         # Color palette data
└── README.md           # Project documentation
```

## Getting Started

1. **Clone or download** the project files
2. **Start a local server**:
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```
3. **Open your browser** and navigate to `http://localhost:8000`

## Usage

### Browsing Palettes
- Browse all palettes or filter by category using the sidebar
- Use the search box to find specific colors or categories
- Switch between grid and list view using the view toggle buttons

### Copying Colors
- **Hover over any color block** to see the HEX code and copy button
- **Click the copy button** to copy the color code to clipboard
- **Click on a color block** to open the color detail modal
- **Use the modal copy button** for additional copying options

### Mobile Experience
- **Tap the filter button** to open the category sidebar
- **Swipe and scroll** naturally through palettes
- **Tap any color** to view details and copy codes
- **Responsive design** adapts to all screen sizes

## Customization

### Adding New Palettes
Edit the `colors.json` file to add new color palettes:

```json
{
  "category": "Your Category",
  "colors": ["#COLOR1", "#COLOR2", "#COLOR3", "#COLOR4"]
}
```

### Modifying Categories
Categories are automatically generated from the JSON data. Simply add palettes with new category names to create new filter options.

### Styling Changes
Modify `styles.css` to customize:
- Color scheme (CSS custom properties in `:root`)
- Layout and spacing
- Typography and fonts
- Animations and transitions

### Functionality Updates
Update `script.js` to modify:
- Search and filter logic
- Palette rendering
- User interactions
- Data processing

## Browser Support

- **Modern Browsers** - Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile Browsers** - iOS Safari 12+, Chrome Mobile 60+
- **Features Used** - CSS Grid, Flexbox, ES6+, Fetch API, Clipboard API

## Performance

- **Lazy Loading** - Palettes load progressively as needed
- **Efficient Rendering** - Virtual scrolling for large datasets
- **Optimized Images** - No external images, pure CSS styling
- **Minimal Dependencies** - No external libraries or frameworks

## Accessibility

- **Keyboard Navigation** - Full keyboard support
- **Screen Readers** - Proper ARIA labels and semantic HTML
- **High Contrast** - Supports high contrast mode
- **Reduced Motion** - Respects user motion preferences
- **Focus Management** - Clear focus indicators

## License

This project is open source and available under the [MIT License](LICENSE).

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Credits

Inspired by ColorHunt.co and designed for designers and developers who need quick access to beautiful color palettes.

---

Made with ❤️ for the design community
