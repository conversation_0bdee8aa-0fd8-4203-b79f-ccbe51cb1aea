/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #f8fafc;
    --accent-color: #10b981;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --background: #ffffff;
    --background-secondary: #f9fafb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --transition: all 0.2s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-secondary);
    font-size: 14px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Styles */
.header {
    background: var(--background);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.25rem;
}

.logo i {
    font-size: 1.5rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    justify-content: flex-end;
}

.search-container {
    position: relative;
    max-width: 400px;
    flex: 1;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    background: var(--background-secondary);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0.1);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
    font-size: 0.875rem;
}

.clear-search {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.clear-search:hover {
    color: var(--text-secondary);
    background: var(--border-light);
}

.toggle-filters {
    display: none;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.toggle-filters:hover {
    background: var(--primary-dark);
}

/* Main Layout */
.main {
    min-height: calc(100vh - 80px);
    padding: 2rem 0;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 2rem;
    align-items: start;
}

/* Sidebar Styles */
.sidebar {
    background: var(--background);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    position: sticky;
    top: 100px;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-light);
}

.sidebar-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-sidebar {
    display: none;
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.close-sidebar:hover {
    color: var(--text-secondary);
    background: var(--border-light);
}

.filter-section {
    margin-bottom: 2rem;
}

.filter-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    background: none;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    text-align: left;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.filter-btn:hover {
    background: var(--background-secondary);
    border-color: var(--border-color);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.filter-name {
    font-weight: 500;
}

.filter-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.125rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.filter-btn.active .filter-count {
    background: rgba(255, 255, 255, 0.2);
}

.filter-btn:not(.active) .filter-count {
    background: var(--border-light);
    color: var(--text-light);
}

.reset-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
}

.reset-btn:hover {
    background: var(--border-light);
    border-color: var(--text-light);
}

/* Palette Section */
.palette-section {
    min-height: 600px;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    gap: 1rem;
}

.section-info h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.section-info p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.view-options {
    display: flex;
    gap: 0.5rem;
}

.view-btn {
    padding: 0.5rem;
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.view-btn:hover {
    background: var(--background-secondary);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Loading and Error States */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-light);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: var(--text-secondary);
    font-size: 1rem;
}

.error-state, .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}

.error-state i, .empty-state i {
    font-size: 3rem;
    color: var(--text-light);
    margin-bottom: 1rem;
}

.error-state h3, .empty-state h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.error-state p, .empty-state p {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

.retry-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.retry-btn:hover {
    background: var(--primary-dark);
}

/* Palette Grid */
.palette-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.palette-card {
    background: var(--background);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition-slow);
    cursor: pointer;
    position: relative;
}

.palette-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.color-blocks {
    display: flex;
    flex-direction: column;
    height: 200px;
}

.color-block {
    flex: 1;
    position: relative;
    transition: var(--transition);
    overflow: hidden;
}

.color-block:hover {
    transform: scale(1.02);
    z-index: 10;
}

.color-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.color-block:hover .color-overlay {
    opacity: 1;
}

.color-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.hex-code {
    color: white;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.copy-color-btn {
    padding: 0.375rem 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.copy-color-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

.palette-info {
    padding: 1rem;
    text-align: center;
}

.palette-category {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--background-secondary);
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: var(--radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Load More */
.load-more-container {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    background: var(--background);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.load-more-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--background-secondary);
}

/* Footer */
.footer {
    background: var(--background);
    border-top: 1px solid var(--border-color);
    margin-top: 4rem;
    padding: 3rem 0 1.5rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.footer-section p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.footer-section ul li:hover {
    color: var(--primary-color);
    cursor: pointer;
}

.footer-bottom {
    padding-top: 2rem;
    border-top: 1px solid var(--border-light);
    text-align: center;
}

.footer-bottom p {
    color: var(--text-light);
    font-size: 0.875rem;
}

.footer-bottom i {
    color: #ef4444;
}

/* Toast Notification */
.toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--accent-color);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    transform: translateY(100px);
    opacity: 0;
    transition: var(--transition-slow);
    z-index: 1000;
}

.toast.show {
    transform: translateY(0);
    opacity: 1;
}

.toast i {
    font-size: 1.125rem;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-slow);
    z-index: 1000;
    padding: 1rem;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.color-modal {
    background: var(--background);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 400px;
    width: 100%;
    transform: scale(0.9);
    transition: var(--transition-slow);
}

.modal-overlay.show .color-modal {
    transform: scale(1);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid var(--border-light);
}

.modal-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--text-secondary);
    background: var(--border-light);
}

.modal-content {
    padding: 1.5rem;
}

.color-preview {
    width: 100%;
    height: 120px;
    border-radius: var(--radius-lg);
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.color-code label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.code-container {
    display: flex;
    gap: 0.5rem;
}

.code-container input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.875rem;
    background: var(--background-secondary);
}

.copy-btn {
    padding: 0.75rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
}

.copy-btn:hover {
    background: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .content-wrapper {
        grid-template-columns: 250px 1fr;
        gap: 1.5rem;
    }

    .palette-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 1.25rem;
    }

    .color-blocks {
        height: 180px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        padding: 0.75rem 0;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .search-container {
        max-width: none;
        flex: 1;
    }

    .toggle-filters {
        display: flex;
    }

    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 200;
        max-height: 100vh;
        border-radius: 0;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .close-sidebar {
        display: block;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .section-info h2 {
        font-size: 1.5rem;
    }

    .palette-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 1rem;
    }

    .color-blocks {
        height: 160px;
    }

    .main {
        padding: 1.5rem 0;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .toast {
        bottom: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }

    .modal-overlay {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .logo h1 {
        display: none;
    }

    .header-content {
        padding: 0.5rem 0;
    }

    .search-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .palette-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.75rem;
    }

    .color-blocks {
        height: 140px;
    }

    .palette-info {
        padding: 0.75rem;
    }

    .section-info h2 {
        font-size: 1.25rem;
    }

    .section-info p {
        font-size: 0.875rem;
    }
}

/* List View Styles */
.palette-grid.list-view {
    grid-template-columns: 1fr;
    gap: 1rem;
}

.palette-grid.list-view .palette-card {
    display: flex;
    align-items: center;
    padding: 1rem;
}

.palette-grid.list-view .color-blocks {
    width: 200px;
    height: 60px;
    flex-direction: row;
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-right: 1rem;
}

.palette-grid.list-view .color-block {
    flex: 1;
}

.palette-grid.list-view .palette-info {
    padding: 0;
    text-align: left;
    flex: 1;
}

@media (max-width: 768px) {
    .palette-grid.list-view .palette-card {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
    }

    .palette-grid.list-view .color-blocks {
        width: 100%;
        height: 80px;
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .palette-grid.list-view .palette-info {
        text-align: center;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
button:focus-visible,
input:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.palette-card:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3);
    }
}
