<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorPalette Pro - Beautiful Color Palettes for Designers</title>
    <meta name="description" content="Discover beautiful color palettes organized by categories. Perfect for designers and developers.">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎨</text></svg>">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-palette"></i>
                    <h1>ColorPalette Pro</h1>
                </div>
                <div class="header-actions">
                    <div class="search-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="searchInput" placeholder="Search by color code or category..." class="search-input">
                        <button id="clearSearch" class="clear-search" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <button id="toggleFilters" class="toggle-filters">
                        <i class="fas fa-filter"></i>
                        <span>Filters</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <div class="content-wrapper">
                <!-- Sidebar Filters -->
                <aside class="sidebar" id="sidebar">
                    <div class="sidebar-header">
                        <h3>Categories</h3>
                        <button id="closeSidebar" class="close-sidebar">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="filter-section">
                        <button class="filter-btn active" data-category="all">
                            <span class="filter-name">All Palettes</span>
                            <span class="filter-count" id="allCount">0</span>
                        </button>
                        <div class="category-filters" id="categoryFilters">
                            <!-- Categories will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="sidebar-footer">
                        <button id="resetFilters" class="reset-btn">
                            <i class="fas fa-refresh"></i>
                            Reset Filters
                        </button>
                    </div>
                </aside>

                <!-- Color Palette Grid -->
                <section class="palette-section">
                    <div class="section-header">
                        <div class="section-info">
                            <h2 id="sectionTitle">All Color Palettes</h2>
                            <p id="sectionSubtitle">Discover beautiful color combinations for your next project</p>
                        </div>
                        <div class="view-options">
                            <button id="gridView" class="view-btn active" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button id="listView" class="view-btn" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div class="loading" id="loading">
                        <div class="loading-spinner"></div>
                        <p>Loading beautiful palettes...</p>
                    </div>

                    <!-- Error State -->
                    <div class="error-state" id="errorState" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Oops! Something went wrong</h3>
                        <p>We couldn't load the color palettes. Please try refreshing the page.</p>
                        <button onclick="location.reload()" class="retry-btn">
                            <i class="fas fa-refresh"></i>
                            Try Again
                        </button>
                    </div>

                    <!-- Empty State -->
                    <div class="empty-state" id="emptyState" style="display: none;">
                        <i class="fas fa-search"></i>
                        <h3>No palettes found</h3>
                        <p>Try adjusting your search or filter criteria.</p>
                    </div>

                    <!-- Palette Grid -->
                    <div class="palette-grid" id="paletteGrid">
                        <!-- Palette cards will be populated by JavaScript -->
                    </div>

                    <!-- Load More Button -->
                    <div class="load-more-container" id="loadMoreContainer" style="display: none;">
                        <button id="loadMoreBtn" class="load-more-btn">
                            <i class="fas fa-plus"></i>
                            Load More Palettes
                        </button>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <i class="fas fa-palette"></i>
                        <span>ColorPalette Pro</span>
                    </div>
                    <p>Beautiful color palettes for designers and developers.</p>
                </div>
                <div class="footer-section">
                    <h4>Features</h4>
                    <ul>
                        <li>10,000+ Color Palettes</li>
                        <li>25+ Categories</li>
                        <li>One-Click Copy</li>
                        <li>Mobile Responsive</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Categories</h4>
                    <ul>
                        <li>Vintage & Retro</li>
                        <li>Modern & Neon</li>
                        <li>Nature & Earth</li>
                        <li>Seasonal Themes</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 ColorPalette Pro. Made with <i class="fas fa-heart"></i> for designers.</p>
            </div>
        </div>
    </footer>

    <!-- Toast Notification -->
    <div class="toast" id="toast">
        <i class="fas fa-check-circle"></i>
        <span id="toastMessage">Color copied to clipboard!</span>
    </div>

    <!-- Color Preview Modal -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="color-modal" id="colorModal">
            <div class="modal-header">
                <h3>Color Details</h3>
                <button class="close-modal" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <div class="color-preview" id="colorPreview"></div>
                <div class="color-info">
                    <div class="color-code">
                        <label>HEX Code:</label>
                        <div class="code-container">
                            <input type="text" id="hexCode" readonly>
                            <button class="copy-btn" id="copyHex">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
